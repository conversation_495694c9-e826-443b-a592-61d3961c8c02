// js/main.js

fetch('components/header/header.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('header').innerHTML = data;
  });

fetch('components/hero/hero.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('hero').innerHTML = data;
  });

fetch('components/ManagementFeatures/ManagementFeatures.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('ManagementFeatures').innerHTML = data;
  });

fetch('components/SocialMediaIcons/SocialMediaIcons.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('SocialMediaIcons').innerHTML = data;
});

fetch('components/InfoCard/InfoCard.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('InfoCard').innerHTML = data;
});

fetch('components/AboutUsHero/AboutUsHero.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('AboutUsHero').innerHTML = data;
});

fetch('components/Thoughts/Thoughts.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Thoughts').innerHTML = data;
});

fetch('components/Testimonials/Testimonials.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Testimonials').innerHTML = data;
});

fetch('components/TextReveal/TextReveal.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('TextReveal').innerHTML = data;
});

fetch('components/FAQ/FAQ.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('FAQ').innerHTML = data;
});

fetch('components/footer/footer.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('footer').innerHTML = data;
  });
