/* Base Reset & Typography */
body {
  margin: 0;
  padding: 0;
  font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

/* Navbar Container */
.navbar {
  padding: 0.75rem 0;
  transition: all 0.3s ease;
}

/* Logo Styles */
.navbar-brand img {
  transition: transform 0.3s ease;
}

.navbar-brand img:hover {
  transform: scale(1.05);
}

/* Toggle Button */
.navbar-toggler {
  border: none;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.navbar-toggler:focus {
  box-shadow: none;
  outline: none;
}

/* Navigation Links Container */
.bg-light-gradient {
  background: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.05));
  border-radius: 50px;
  padding: 0.25rem;
}

/* Individual Nav Links */
.nav-link {
  color: #939393 !important;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 1rem !important;
  margin: 0 0.25rem;
}

.nav-link:hover,
.nav-link.active {
  color: #563D39 !important;
  font-weight: 600;
}

/* Button Styles */
.btn-primary {
  background-color: #563D39 !important;
  border-color: #563D39 !important;
  border-radius: 6px !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #4a332f !important;
  border-color: #4a332f !important;
  transform: translateY(-1px);
}

.btn-link {
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-link:hover {
  text-decoration: underline;
}

/* Mobile Styles (Below 992px) */
@media (max-width: 991.98px) {
  .navbar-collapse {
    background-color: white;
    padding: 1.5rem;
    margin-top: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }
  
  .bg-light-gradient {
    background: none !important;
    padding: 0 !important;
  }
  
  .nav-link {
    padding: 0.75rem 0 !important;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    margin: 0 !important;
  }
  
  .nav-link:last-child {
    border-bottom: none;
  }
  
  /* Mobile buttons container */
  .d-flex.d-lg-none.flex-column {
    padding: 1.5rem 0 0.5rem;
    margin-top: 1rem;
    border-top: 1px solid rgba(0,0,0,0.1);
    gap: 1rem !important;
  }
  
  /* Full width buttons on mobile */
  .navbar-nav,
  .d-flex.d-lg-none.flex-column .btn {
    width: 100%;
  }
}

/* Small Mobile Devices (Below 576px) */
@media (max-width: 575.98px) {
  .navbar-brand img {
    width: 100px;
  }
  
  .navbar-toggler img {
    width: 32px;
    height: 32px;
  }
  
  .navbar-collapse {
    padding: 1rem;
  }
}

/* Animation for dropdown */
.collapsing {
  transition: height 0.3s ease;
}

/* Ensure navbar stays on top */
.navbar.fixed-top {
  z-index: 1030;
}

/* Container adjustments for mobile */
@media (max-width: 767.98px) {
  .container {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
}

/* Arrow icon in login button */
.btn-primary img {
  transition: transform 0.3s ease;
}

.btn-primary:hover img {
  transform: translateX(3px);
}














/* -------------------------- hero page --------------------------- */

/* Custom CSS for Social Media Hero Section */

/* Global Styles */
.hero-section {
    position: relative;
    z-index: 1;
    opacity: 1;
    visibility: visible;
    background-color: white !important;
    padding-left: 2rem;
    padding-right: 2rem;
}

/* Typography */
.text-brown {
    color: #563D39;
}

.hero-title {
    font-size: 1.25rem;
    line-height: 110%;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    color: #563D39;
    font-size: 1.25rem;
    line-height: 110%;
    font-weight: 500;
}

.hero-description {
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.75rem;
    line-height: 140%;
    font-weight: 300;
}

/* Content Layout */
.hero-content {
    margin-top: 74px;
}

.content-wrapper {
    width: 100%;
}

/* Gallery Container */
.gallery-container {
    width: 100%;
    min-height: 280px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Skeleton Loaders */
.skeleton-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.skeleton-circle {
    width: 120px;
    height: 120px;
    background-color: #e5e7eb;
    border-radius: 50%;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-rect {
    width: 220px;
    background-color: #e5e7eb;
    border-radius: 1rem;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-rect-1 { height: 150px; }
.skeleton-rect-2 { height: 270px; }
.skeleton-rect-3 { height: 150px; }
.skeleton-rect-4 { height: 220px; }
.skeleton-rect-5 { height: 220px; }
.skeleton-rect-6 { height: 150px; }
.skeleton-rect-7 { height: 220px; }
.skeleton-rect-8 { height: 210px; }

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Mobile Gallery - UPDATED */
.mobile-gallery {
    width: 100%;
    padding-bottom: 20px;
    overflow: visible;
}

.scrolling-row-container {
    width: 100%;
    height: 150px;
    overflow: visible;
    margin-bottom: 20px;
    position: relative;
}

.scrolling-row {
    display: flex;
    height: 100%;
    align-items: center;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.scroll-left {
    animation: scrollLeft 20s linear infinite;
}

.scroll-right {
    animation: scrollRight 20s linear infinite;
}

.mobile-image-wrapper {
    flex-shrink: 0;
    margin-right: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.mobile-image {
    width: 130px;
    height: 130px;
    object-fit: cover;
    border-radius: 50%;
    backface-visibility: hidden;
    will-change: transform;
}

/* Desktop Gallery */
.desktop-gallery {
    width: 100%;
}

.scrolling-column-container {
    height: 100vh;
    overflow: hidden;
}

.scrolling-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.scroll-up {
    animation: scrollUp 30s linear infinite;
}

.scroll-down {
    animation: scrollDown 30s linear infinite;
}

.desktop-image-wrapper {
    margin-bottom: 1rem;
}

.desktop-image {
    width: 220px;
    object-fit: cover;
    border-radius: 1rem;
    loading: lazy;
    decoding: async;
}

.desktop-image-1 { height: 150px; }
.desktop-image-2 { height: 270px; }
.desktop-image-3 { height: 150px; }
.desktop-image-4 { height: 220px; }
.desktop-image-5 { height: 220px; }
.desktop-image-6 { height: 150px; }
.desktop-image-7 { height: 220px; }
.desktop-image-8 { height: 210px; }

/* Animations */
@keyframes scrollLeft {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

@keyframes scrollRight {
    0% {
        transform: translateX(-50%);
    }
    100% {
        transform: translateX(0);
    }
}

@keyframes scrollUp {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-50%);
    }
}

@keyframes scrollDown {
    0% {
        transform: translateY(-50%);
    }
    100% {
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (min-width: 576px) and (max-width: 767px) {
    .hero-title, .hero-subtitle {
        font-size: 2rem;
    }
    .hero-description {
        font-size: 1.125rem;
    }

    .mobile-gallery {
        width: 100%;
    }

    .mobile-image {
        width: 140px;
        height: 140px;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .hero-section {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    
    .hero-content {
        margin-top: 0;
    }
    
    .content-wrapper {
        max-width: 615px;
        min-height: 336px;
    }
    
    .hero-title, .hero-subtitle {
        font-size: 3.125rem;
    }
    
    .hero-description {
        font-size: 1.25rem;
    }
    
    .gallery-container {
        width: 540px;
        min-height: 700px;
    }
    
    .mobile-gallery {
        padding-bottom: 0;
    }
    
    .skeleton-rect {
        width: 220px;
    }
}

@media (min-width: 992px) {
    .hero-section {
        padding-left: 3rem;
        padding-right: 3rem;
    }
    
    .hero-title, .hero-subtitle {
        font-size: 4.375rem;
    }
    
    .desktop-image {
        width: 250px;
    }
    
    .skeleton-rect {
        width: 250px;
    }
    
    .desktop-image-1 { height: 169px; }
    .desktop-image-2 { height: 310px; }
    .desktop-image-3 { height: 173px; }
    .desktop-image-4 { height: 250px; }
    .desktop-image-5 { height: 250px; }
    .desktop-image-6 { height: 169px; }
    .desktop-image-7 { height: 250px; }
    .desktop-image-8 { height: 240px; }
    
    .skeleton-rect-1 { height: 169px; }
    .skeleton-rect-2 { height: 310px; }
    .skeleton-rect-3 { height: 173px; }
    .skeleton-rect-4 { height: 250px; }
    .skeleton-rect-5 { height: 250px; }
    .skeleton-rect-6 { height: 169px; }
    .skeleton-rect-7 { height: 250px; }
    .skeleton-rect-8 { height: 240px; }
}

/* Ensure proper display based on screen size */
.mobile-skeleton,
.mobile-gallery {
    display: none;
}

.desktop-skeleton,
.desktop-gallery {
    display: none;
}

/* Show mobile on mobile */
@media (max-width: 767.98px) {
    .mobile-skeleton,
    .mobile-gallery {
        display: block;
    }
}

/* Show desktop on tablet/desktop */
@media (min-width: 768px) {
    .desktop-skeleton,
    .desktop-gallery {
        display: flex;
    }
}

/* Performance Optimizations */
.scrolling-row,
.scrolling-column,
.mobile-image,
.desktop-image {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Ensure smooth animations */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}






















/*---------------------- Management Features --------------------------- */




/* Management Features Section Styles */
.management-features-section {
    padding: 4rem 1rem;
    background-image: url('Background.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 600px;
    position: relative;
    z-index: 1;
}

.features-container {
    background-color: transparent;
    border-radius: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Solutions Badge */
.solutions-badge {
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.1);
    width: 104px;
    height: 38px;
    padding: 10px;
    border-radius: 7px;
    backdrop-filter: blur(10px);
}

.header-icon {
    width: 18px;
    height: 18px;
}

.badge-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
}

/* Section Title */
.section-title {
    color: white;
    font-size: 1.5rem;
    font-weight: 500;
    line-height: 1.4;
}

@media (min-width: 768px) {
    .section-title {
        font-size: 1.875rem;
    }
}

/* Feature Cards */
.feature-card {
    background-color: rgba(255, 255, 255, 0.06);
    border-radius: 20px;
    padding: 20px;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 330px;
}

@media (max-width: 767px) {
    .feature-card {
        min-height: auto;
    }
}

.feature-card:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Feature Icons */
.feature-icon-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.feature-icon {
    width: 48px;
    height: 48px;
    transition: opacity 0.3s ease-in-out;
}

/* Feature Titles */
.feature-title {
    color: #FFFFFF;
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.3;
    margin-bottom: 0.75rem;
}

@media (min-width: 768px) {
    .feature-title {
        font-size: 1rem;
    }
}

/* Feature Descriptions */
.feature-description {
    color: #E4E0DF;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .feature-description {
        font-size: 0.875rem;
    }
}

/* Read More Button */
.read-more-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    font-weight: 400;
    padding: 4px 4px 4px 20px;
    width: 142px;
    height: 38px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.read-more-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.6);
    color: white;
}

.read-more-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.arrow-icon {
    width: 30px;
    height: 30px;
    transition: transform 0.2s ease;
}

.read-more-btn:hover .arrow-icon {
    transform: translateX(2px);
}

/* Responsive Adjustments */
@media (max-width: 575px) {
    .management-features-section {
        padding: 2rem 0.5rem;
    }
    
    .feature-title {
        font-size: 1.125rem;
    }
    
    .feature-description {
        font-size: 0.9rem;
    }
}

@media (min-width: 992px) {
    .features-container {
        max-width: 1400px;
    }
}

/* Loading States */
.feature-icon[style*="opacity: 0"] {
    opacity: 0 !important;
}

.feature-icon[style*="opacity: 1"] {
    opacity: 1 !important;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .feature-card,
    .read-more-btn,
    .arrow-icon,
    .feature-icon {
        transition: none;
    }
    
    .read-more-btn:hover .arrow-icon {
        transform: none;
    }
    
    .feature-card:hover {
        transform: none;
    }
}

/* Focus styles for better accessibility */
.read-more-btn:focus-visible {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

















/* ------------------- SocialMediaIcons ------------------------ */

/* Social Media Icons Carousel Styles */

.social-media-section {
    width: 100%;
    padding: 144px 16px;
    background-color: #FFFFFF;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.content-wrapper {
    text-align: center;
    position: relative;
    z-index: 10;
    width: 100%;
    margin-top: -20px;
}

.section-title {
    font-size: 43px;
    font-weight: 500;
    color: #563D39;
    margin-bottom: 24px;
    line-height: 1.2;
}

.section-subtitle {
    min-height: 10vh;
    font-size: 24px;
    color: #00000099;
    max-width: 768px;
    margin: 0 auto 78px;
    font-weight: 300;
}

.icons-container {
    position: relative;
}

.background-images {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.bg-shadow,
.bg-gradient {
    position: absolute;
    width: 583px;
    height: 500px;
}

.carousel-container {
    position: relative;
    overflow: hidden;
    height: 150px;
}

.icons-track {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 1s ease-in-out;
}

.icons-track.restarting {
    opacity: 0.3;
}

.social-icon {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9px;
    transition: all 1s ease-in-out;
    flex-shrink: 0;
}

.icon-bracket {
    position: absolute;
    width: 24px;
    height: 24px;
    border-width: 2px;
    border-style: solid;
    border-color: #d1d5db;
    z-index: 20;
    transition: border-color 1s ease-in-out;
}

.bracket-tl {
    top: -12px;
    left: -12px;
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 10px;
}

.bracket-tr {
    top: -12px;
    right: -12px;
    border-left: none;
    border-bottom: none;
    border-top-right-radius: 10px;
}

.bracket-bl {
    bottom: -12px;
    left: -12px;
    border-right: none;
    border-top: none;
    border-bottom-left-radius: 10px;
}

.bracket-br {
    bottom: -12px;
    right: -12px;
    border-left: none;
    border-top: none;
    border-bottom-right-radius: 10px;
}

.icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9px;
    transition: all 1s ease-in-out;
    background-color: #f8f9fa;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    flex-shrink: 0;
}

.icon-container.highlighted {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.social-icon-img {
    object-fit: contain;
    position: relative;
    z-index: 10;
    transition: all 1s ease-in-out;
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1199.98px) {
    .section-title {
        font-size: 38px;
    }
}

@media (max-width: 991.98px) {
    .section-title {
        font-size: 38px;
    }
    
    .section-subtitle {
        font-size: 20px;
    }
}

@media (max-width: 767.98px) {
    .section-title {
        font-size: 30px;
    }
    
    .section-subtitle {
        font-size: 16px;
    }
    
    .carousel-container {
        height: 120px;
    }
    
    .bracket-tl,
    .bracket-bl {
        left: -24px;
    }
    
    .bracket-tr,
    .bracket-br {
        right: -24px;
    }
}

@media (max-width: 640px) {
    .social-media-section {
        padding: 144px 16px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Platform Colors */
.youtube { color: #FF0000; }
.reddit { color: #FF4500; }
.instagram { color: #E4405F; }
.facebook { color: #1877F2; }
.twitter { color: #000000; }
.pinterest { color: #BD081C; }
.linkedin { color: #0A66C2; }
.threads { color: #000000; }

















/* ----------------------------- InfoCard ------------------------------ */

/* InfoCard Component Styles */
.info-card-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

.info-card-container {
    width: 90%;
    margin: 0 auto;
}

.info-card-wrapper {
    background: #563D39;
    border-radius: 40px;
    padding: 2rem;
}

/* Main Feature Card */
.main-feature-card {
    position: relative;
    min-height: 400px;
    padding: 1.5rem;
}

.image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    height: 100%;
    min-height: 350px;
}

.main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 8rem;
    backdrop-filter: blur(4px);
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, transparent 100%);
    pointer-events: none;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
}

.image-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem 1.5rem;
}

.main-title {
    font-size: 1.125rem;
    line-height: 1.4;
    margin: 0;
}

/* Features Container */
.features-container {
    padding: 1rem 0;
}

.feature-item {
    margin-bottom: 2rem;
}

.feature-item:last-child {
    margin-bottom: 0;
}

.feature-icon-container {
    width: 70px;
    height: 70px;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon {
    width: 70px;
    height: 70px;
}

.feature-title {
    font-size: 1rem;
    line-height: 1.4;
}

.feature-description {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (min-width: 576px) {
    .info-card-wrapper {
        padding: 2.5rem;
    }
    
    .main-feature-card {
        min-height: 450px;
    }
    
    .image-container {
        min-height: 400px;
    }
    
    .image-content {
        padding: 1.5rem;
    }
    
    .main-title {
        font-size: 1.25rem;
    }
    
    .feature-item {
        margin-bottom: 3rem;
    }
    
    .feature-icon-container {
        width: 80px;
        height: 80px;
        margin-right: 1.5rem;
    }
    
    .feature-icon {
        width: 80px;
        height: 80px;
    }
    
    .feature-title {
        font-size: 1.125rem;
    }
    
    .feature-description {
        font-size: 1rem;
    }
}

@media (min-width: 992px) {
    .info-card-wrapper {
        padding: 3rem;
    }
    
    .main-feature-card {
        min-height: 500px;
        padding: 2rem;
    }
    
    .image-container {
        min-height: 450px;
    }
    
    .main-title {
        font-size: 1.875rem;
    }
    
    .features-container {
        padding: 0;
    }
    
    .feature-item {
        margin-bottom: 4.375rem;
    }
    
    .feature-title {
        font-size: 1.25rem;
    }
}

/* Additional Bootstrap overrides for exact matching */
.row.g-4 {
    --bs-gutter-x: 2rem;
    --bs-gutter-y: 2rem;
}

@media (min-width: 992px) {
    .row.g-lg-5 {
        --bs-gutter-x: 3rem;
        --bs-gutter-y: 3rem;
    }
}

/* Ensure proper alignment and spacing */
.align-items-stretch > .col-12,
.align-items-stretch > .col-lg-6 {
    display: flex;
    flex-direction: column;
}

.main-feature-card,
.features-container {
    flex: 1;
}













/* --------------------------- AboutUsHero ------------------------- */


/* Custom CSS for About Us Section */

/* Root variables for consistent theming */
:root {
    --primary-color: #563D39;
    --text-secondary: #000000B2;
    --white: #ffffff;
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-hover: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --transition-duration: 300ms;
}

/* Background and spacing utilities */
.bg-white {
    background-color: var(--white) !important;
}

.text-primary-custom {
    color: var(--primary-color) !important;
}

/* Custom container width */
.custom-container {
    width: 90%;
    max-width: 100%;
}

/* Custom margin bottom */
.mb-custom {
    margin-bottom: 50px;
}

@media (min-width: 768px) {
    .mb-custom {
        margin-bottom: 64px;
    }
}

/* Content spacing */
.content-spacing {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Text content styling */
.text-content {
    color: var(--text-secondary);
    font-weight: 400;
    line-height: 28px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Team grid layout */
.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

/* Base team image container */
.team-image-container {
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: box-shadow var(--transition-duration) ease-in-out;
    position: relative;
}

.team-image-container:hover {
    box-shadow: var(--shadow-hover);
}

/* Team image styling */
.team-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-duration) ease-in-out;
}

.team-image-container:hover .team-image {
    transform: scale(1.05);
}

/* Individual team member dimensions and border radius */

/* Team Member 1 */
.team-image-1 {
    width: 120px;
    height: 120px;
    border-radius: 26px;
}

/* Team Member 2 */
.team-image-2 {
    width: 120px;
    height: 120px;
    border-radius: 60px;
}

/* Team Member 3 */
.team-image-3 {
    width: 120px;
    height: 120px;
    border-radius: 114px;
}

/* Team Member 4 */
.team-image-4 {
    width: 120px;
    height: 120px;
    border-radius: 115px;
}

/* Team Member 5 */
.team-image-5 {
    width: 120px;
    height: 120px;
    border-radius: 26px;
}

/* Team Member 6 */
.team-image-6 {
    width: 120px;
    height: 120px;
    border-radius: 60px;
}

/* Small screen responsive adjustments */
@media (min-width: 576px) {
    .team-image-1 {
        width: 140px;
        height: 140px;
    }
    
    .team-image-2 {
        width: 130px;
        height: 150px;
    }
    
    .team-image-3 {
        width: 150px;
        height: 130px;
    }
    
    .team-image-4 {
        width: 135px;
        height: 145px;
    }
    
    .team-image-5 {
        width: 145px;
        height: 135px;
    }
    
    .team-image-6 {
        width: 130px;
        height: 130px;
    }
}

/* Large screen responsive adjustments */
@media (min-width: 992px) {
    .team-image-1,
    .team-image-2,
    .team-image-3,
    .team-image-4,
    .team-image-5,
    .team-image-6 {
        width: 190px;
        height: 190px;
    }
}

/* Additional responsive utilities */
@media (max-width: 575.98px) {
    .custom-container {
        width: 95%;
    }
    
    .team-grid {
        gap: 0.75rem;
    }
}

/* Ensure proper spacing on medium screens */
@media (min-width: 768px) {
    .py-md-5 {
        padding-top: 4rem !important;
        padding-bottom: 4rem !important;
    }
}

/* Fine-tune text sizing for better readability */
@media (max-width: 767.98px) {
    .display-5 {
        font-size: 2.5rem;
    }
}

/* Smooth transitions for all interactive elements */
* {
    transition-property: transform, box-shadow;
    transition-timing-function: ease-in-out;
}

/* Accessibility improvements */
.team-image-container:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .team-image-container:hover .team-image {
        transform: none;
    }
    
    .team-image-container {
        box-shadow: none !important;
    }
}





















/* ----------------------- Testimonials --------------------- */





/* ===== ROOT VARIABLES ===== */
:root {
  --primary-color: #563D39;
  --primary-hover: #6B4A45;
  --text-primary: #563D39;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --border-color: #e5e7eb;
  --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --border-radius: 20px;
  --transition-fast: 0.2s ease-out;
  --transition-medium: 0.3s ease-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== DARK MODE VARIABLES ===== */
[data-bs-theme="dark"] {
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --border-color: #374151;
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color var(--transition-medium), color var(--transition-medium);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
  background-color: var(--bg-primary);
  padding-top: 5rem;
  min-height: 100vh;
  transition: background-color var(--transition-medium);
}

.testimonials-title {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2rem;
  transition: color var(--transition-medium);
}

@media (min-width: 768px) {
  .testimonials-title {
    margin-bottom: 4rem;
  }
}

/* ===== TESTIMONIALS CONTAINER ===== */
.testimonials-container {
  max-width: 1400px;
  overflow: hidden;
}

/* ===== CAROUSEL CUSTOMIZATION ===== */
.carousel-inner {
  padding: 0.5rem;
}

.carousel-item {
  transition: transform var(--transition-slow);
}

/* ===== TESTIMONIAL CARDS ===== */
.testimonial-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  box-shadow: var(--shadow-light);
  transition: all var(--transition-medium);
  cursor: pointer;
  height: 100%;
  min-height: 200px;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@media (min-width: 768px) {
  .testimonial-card {
    padding: 2rem;
    min-height: 280px;
  }
  
  .testimonial-card:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #ffffff;
    transform: scale(1.02) translateZ(0);
    box-shadow: var(--shadow-medium);
  }
  
  .testimonial-card:hover .profile-name,
  .testimonial-card:hover .testimonial-content {
    color: #ffffff;
  }
  
  .testimonial-card:hover .profile-role {
    color: #e5e7eb;
  }
}

/* ===== PROFILE SECTION ===== */
.profile-section {
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .profile-section {
    margin-bottom: 1.5rem;
  }
}

.profile-image {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

@media (min-width: 768px) {
  .profile-image {
    width: 3.5rem;
    height: 3.5rem;
  }
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-medium);
}

.profile-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  transition: color var(--transition-medium);
}

@media (min-width: 768px) {
  .profile-name {
    font-size: 1.125rem;
  }
}

.profile-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
  transition: color var(--transition-medium);
}

@media (min-width: 768px) {
  .profile-role {
    font-size: 0.875rem;
  }
}

/* ===== TESTIMONIAL CONTENT ===== */
.testimonial-content {
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0;
  transition: color var(--transition-medium);
}

@media (min-width: 768px) {
  .testimonial-content {
    font-size: 1rem;
    line-height: 1.7;
  }
}

/* ===== CAROUSEL CONTROLS ===== */
.carousel-controls {
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .carousel-controls {
    gap: 1rem;
  }
}

.carousel-control-btn {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: none;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  cursor: pointer;
  transform: translateZ(0);
}

@media (min-width: 768px) {
  .carousel-control-btn {
    width: 3rem;
    height: 3rem;
  }
}

.carousel-control-btn:hover {
  background-color: var(--border-color);
  transform: scale(1.05) translateZ(0);
}

.carousel-control-btn:active {
  transform: scale(0.95) translateZ(0);
}

.carousel-control-next-btn {
  background-color: var(--primary-color);
  color: #ffffff;
}

.carousel-control-next-btn:hover {
  background-color: var(--primary-hover);
}

.carousel-arrow {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform var(--transition-fast);
}

@media (min-width: 768px) {
  .carousel-arrow {
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* ===== DARK MODE TOGGLE ===== */
#darkModeToggle {
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.dark-mode-icon {
  font-size: 1rem;
  transition: transform var(--transition-fast);
}

#darkModeToggle:hover .dark-mode-icon {
  transform: scale(1.1);
}

/* ===== DARK MODE STYLES ===== */
[data-bs-theme="dark"] .testimonials-section {
  background-color: var(--bg-primary);
}

[data-bs-theme="dark"] .testimonial-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-bs-theme="dark"] .carousel-control-btn {
  background-color: var(--border-color);
  color: var(--text-secondary);
}

[data-bs-theme="dark"] .carousel-control-btn:hover {
  background-color: #4b5563;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 767.98px) {
  .testimonials-section {
    padding-top: 3rem;
  }
  
  .carousel-inner {
    padding: 0.25rem;
  }
  
  .testimonial-card {
    margin-bottom: 1rem;
  }
}

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */
@media (hover: none) and (pointer: coarse) {
  .testimonial-card:hover {
    transform: none;
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
    box-shadow: var(--shadow-light);
  }
  
  .testimonial-card:active {
    transform: scale(0.98);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #ffffff;
  }
  
  .testimonial-card:active .profile-name,
  .testimonial-card:active .testimonial-content {
    color: #ffffff;
  }
  
  .testimonial-card:active .profile-role {
    color: #e5e7eb;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .carousel-item {
    transition: none;
  }
}

/* ===== FOCUS STYLES ===== */
.carousel-control-btn:focus,
#darkModeToggle:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.testimonial-card:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .testimonial-card {
    border-width: 2px;
  }
  
  .carousel-control-btn {
    border: 2px solid var(--text-primary);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .carousel-controls,
  #darkModeToggle {
    display: none !important;
  }
  
  .testimonial-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}

















/* ------------------------ FAQ --------------------- */



/* FAQ Component Styles */
.faq-container {
    width: 90%;
    margin: 2.5rem auto;
    padding: 1.5rem;
}

@media (max-width: 576px) {
    .faq-container {
        padding: 1rem;
    }
}

/* Newsletter Section */
.newsletter-section {
    background-color: white;
    border-radius: 0.5rem;
}

.newsletter-title {
    font-size: 2.25rem;
    font-weight: 500;
    color: #563D39;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .newsletter-title {
        font-size: 1.875rem;
    }
}

.newsletter-description {
    color: rgba(0, 0, 0, 0.6);
    font-weight: 400;
    font-size: 1rem;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.email-form-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.email-input {
    padding: 0.75rem 0.875rem;
    background-color: #F6F6F6;
    border: 1px solid #F6F6F6;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

@media (min-width: 768px) {
    .email-input {
        width: 415px;
        height: 43px;
    }
}

.email-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px #563D39;
    border-color: transparent;
}

.email-input.error:focus {
    box-shadow: 0 0 0 2px #dc3545;
}

.email-input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-custom {
    background-color: #563D39;
    color: white;
    font-weight: 400;
    font-size: 1rem;
    border-radius: 0.375rem;
    padding: 0.375rem 1.25rem 0.375rem 1.25rem;
    border: none;
    transition: all 0.3s ease;
    width: 141px;
    height: 42px;
    gap: 0.5rem;
}

.btn-custom:hover {
    background-color: #4a332f;
    color: white;
}

.btn-custom:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.arrow-icon {
    width: 30px;
    height: 30px;
    transition: transform 0.3s ease;
}

.btn-custom:hover .arrow-icon {
    transform: translateX(2px);
}

.error-message {
    margin-top: 0.25rem;
}

.more-faq-link {
    color: #563D39;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.more-faq-link:hover {
    color: #563D39;
    text-decoration: underline;
}

/* FAQ Accordion Styles */
.faq-accordion {
    background-color: white;
}

.faq-item {
    border: none;
}

.faq-button {
    background-color: white;
    border: none;
    padding: 1rem 1.5rem;
    text-align: left;
    transition: all 0.3s ease;
    cursor: pointer;
}

@media (max-width: 768px) {
    .faq-button {
        padding: 1rem 0;
    }
}

.faq-button:hover {
    background-color: #f8f9fa;
}

.faq-button:focus {
    outline: none;
    box-shadow: none;
}

.faq-title {
    font-size: 1.25rem;
    font-weight: 300;
    color: #000000;
    flex: 1;
    margin-right: 1rem;
}

.icon-container {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.plus-icon,
.minus-icon {
    width: 28px;
    height: 28px;
    transition: all 0.3s ease;
}

.faq-collapse {
    transition: all 0.5s ease-in-out;
}

.faq-content {
    padding: 0 1.5rem 1.5rem 1.5rem;
    background-color: white;
}

@media (max-width: 768px) {
    .faq-content {
        padding: 0 0 1.5rem 0;
    }
}

.faq-text {
    color: rgba(0, 0, 0, 0.6);
    font-weight: 400;
    font-size: 1.125rem;
    line-height: 1.6;
    margin: 0;
}

.faq-divider {
    border: none;
    border-top: 1px solid #e9ecef;
    margin: 0;
    opacity: 1;
}

.faq-item:last-child .faq-divider {
    display: none;
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Smooth transitions for accordion icons */
.faq-button[aria-expanded="true"] .plus-icon {
    opacity: 0;
    transform: rotate(90deg);
}

.faq-button[aria-expanded="true"] .minus-icon {
    opacity: 1;
    transform: rotate(0deg);
}

.faq-button[aria-expanded="false"] .plus-icon {
    opacity: 1;
    transform: rotate(0deg);
}

.faq-button[aria-expanded="false"] .minus-icon {
    opacity: 0;
    transform: rotate(-90deg);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .faq-container {
        margin-top: 1.5rem;
    }
    
    .newsletter-title {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .newsletter-title {
        font-size: 1.75rem;
    }
    
    .faq-title {
        font-size: 1.125rem;
    }
    
    .faq-text {
        font-size: 1rem;
    }
    
    .btn-custom {
        width: 100%;
        margin-top: 0.5rem;
    }
}













































/* Blogs page */


/* Stories Banner Section */
.banner-box {
  background-image: url("/assets/Blogs/Blog_bg.svg");
  background-size: cover;
  background-blend-mode: multiply;
  margin-right: 20px;
  margin-left: 20px;
}

/* Mobile (XS) - Default */
.banner-box h2 {
  font-size: 24px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.banner-box .lead {
  font-size: 16px;
  opacity: 0.9;
}

/* Small devices (SM - 576px and up) */
@media (min-width: 576px) {
  .banner-box h2 {
    font-size: 26px;
  }
  
  .banner-box .lead {
    font-size: 17px;
  }
}

/* Medium devices (MD - 768px and up) - Tablet */
@media (min-width: 768px) {
  .banner-box h2 {
    font-size: 30px;
  }
  
  .banner-box .lead {
    font-size: 18px;
  }
}

/* Large devices (LG - 992px and up) - Web */
@media (min-width: 992px) {
  .banner-box h2 {
    font-size: 36px;
  }
  
  .banner-box .lead {
    font-size: 20px;
  }
}

/* Extra large devices (XL - 1200px and up) */
@media (min-width: 1200px) {
  .banner-box h2 {
    font-size: 38px;
  }
  
  .banner-box .lead {
    font-size: 21px;
  }
}

/* XXL devices (1400px and up) */
@media (min-width: 1400px) {
  .banner-box h2 {
    font-size: 40px;
  }
  
  .banner-box .lead {
    font-size: 22px;
  }
}

/* Adjust spacing to account for fixed navbar */
body {
  padding-top: 80px;
}

@media (max-width: 991.98px) {
  body {
    padding-top: 70px;
  }
}
