// Image preloading and gallery management
class GalleryManager {
    constructor() {
        this.imageCache = new Set();
        this.loadingPromises = new Map();
        this.imagesLoaded = false;
        this.hasBeenRendered = false;
        
        this.allImageSrcs = [
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Pinterest-FzfmcjwC64pAblKDSUsjKTQbufmTZP.svg',
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Dummy-01-h4ZU5Hd8ZSDUBEVxzgCHiVCnZxRlDJ.svg',
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Instagram-ZsxfbMpBR2y46jQaa1RGiegQ4drfFT.svg',
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Dummy-02-auw0fYGYyBF07Ql6tFcvHdIux1mocP.svg',
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Dummy-03-aMlGgRVmE842J904QVvj1vW2kxQDzS.svg',
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/LinkedIn-lzGIuJnNtb3qQkEep8VeQnvqlIlkwD.svg',
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Dummy-04-4Bu76L0OLfDMZEL6NQo7T2atZGXoXb.svg',
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Dummy-05-RJO3zt62e16uDeZKPuSqzQdTwnOg04.svg'
        ];
        
        this.init();
    }
    
    init() {
        // Start loading images immediately
        this.preloadImages();
        
        // Mark component as rendered
        this.hasBeenRendered = true;
    }
    
    preloadImages() {
        // If already loaded, show immediately
        if (this.imagesLoaded) {
            this.showGallery();
            return;
        }
        
        // Check if images are already cached
        const allCached = this.allImageSrcs.every(src => this.imageCache.has(src));
        
        if (allCached) {
            this.imagesLoaded = true;
            this.showGallery();
            return;
        }
        
        let loaded = 0;
        const total = this.allImageSrcs.length;
        
        // Add timeout to prevent infinite loading
        const fallbackTimeout = setTimeout(() => {
            this.imagesLoaded = true;
            this.showGallery();
        }, 1500);
        
        const checkComplete = () => {
            loaded++;
            if (loaded === total) {
                clearTimeout(fallbackTimeout);
                this.imagesLoaded = true;
                this.showGallery();
            }
        };
        
        this.allImageSrcs.forEach(src => {
            // Check if already cached
            if (this.imageCache.has(src)) {
                checkComplete();
                return;
            }
            
            // Check if already loading
            if (this.loadingPromises.has(src)) {
                this.loadingPromises.get(src).then(checkComplete).catch(checkComplete);
                return;
            }
            
            // Create new loading promise
            const loadPromise = new Promise((resolve, reject) => {
                const img = new Image();
                img.src = src;
                img.onload = () => {
                    this.imageCache.add(src);
                    this.loadingPromises.delete(src);
                    resolve();
                };
                img.onerror = () => {
                    this.loadingPromises.delete(src);
                    reject();
                };
            });
            
            this.loadingPromises.set(src, loadPromise);
            loadPromise.then(checkComplete).catch(checkComplete);
        });
    }
    
    showGallery() {
        const skeleton = document.getElementById('loading-skeleton');
        const gallery = document.getElementById('gallery-content');
        
        if (skeleton && gallery) {
            skeleton.style.display = 'none';
            gallery.style.display = 'block';
            
            // Trigger animations
            this.startAnimations();
        }
    }
    
    startAnimations() {
        // Animations are handled by CSS, but we can add any additional
        // JavaScript-based animations or interactions here if needed
        
        // Ensure smooth performance
        const animatedElements = document.querySelectorAll('.scrolling-row, .scrolling-column');
        animatedElements.forEach(element => {
            element.style.willChange = 'transform';
            element.style.transform = 'translateZ(0)';
            element.style.backfaceVisibility = 'hidden';
        });
    }
    
    // Method to handle responsive behavior
    handleResize() {
        // Add any resize-specific logic here if needed
        // The CSS handles most of the responsive behavior
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize gallery manager
    const galleryManager = new GalleryManager();
    
    // Handle window resize
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            galleryManager.handleResize();
        }, 250);
    });
    
    // Prevent layout shift by ensuring container maintains size
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        heroSection.style.opacity = '1';
        heroSection.style.visibility = 'visible';
    }
});













